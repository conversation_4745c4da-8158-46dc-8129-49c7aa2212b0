#!/bin/bash

# Script to check code formatting, compilation, and run tests locally
# Run this before pushing to ensure CI will pass

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if clang-format is installed
if ! command -v clang-format &> /dev/null; then
    print_error "clang-format is not installed. Please install it first."
    exit 1
fi

# Check if clang++ is installed
if ! command -v clang++ &> /dev/null; then
    print_error "clang++ is not installed. Please install it first."
    exit 1
fi

echo
echo "Checking code formatting..."
echo "------------------------------"

# Find all C++ files and check formatting
CPP_FILES=$(find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" 2>/dev/null || true)

if [ -z "$CPP_FILES" ]; then
    print_warning "No C++ files found in src directory"
else
    FORMAT_ISSUES=0
    for file in $CPP_FILES; do
        if ! clang-format --dry-run --Werror "$file" &>/dev/null; then
            print_error "Formatting issues in: $file"
            FORMAT_ISSUES=1
        fi
    done
    
    if [ $FORMAT_ISSUES -eq 0 ]; then
        print_status "All files are properly formatted"
    else
        echo
        print_warning "To fix formatting issues, run:"
        echo "find src -name '*.cpp' -o -name '*.hpp' -o -name '*.h' | xargs clang-format -i"
        echo
        read -p "Do you want to auto-fix formatting now? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | xargs clang-format -i
            print_status "Formatting fixed!"
        else
            print_error "Please fix formatting issues before continuing"
            exit 1
        fi
    fi
fi