name: C++ CI

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  format-check:
    name: Code Formatting Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install clang-format
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format
        
    - name: Check code formatting
      run: |
        # Find all C++ source files and check formatting
        find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | xargs clang-format --dry-run --Werror
        
    - name: Show formatting diff (if any)
      if: failure()
      run: |
        echo "Code formatting issues found. Run the following to fix:"
        echo "find src -name '*.cpp' -o -name '*.hpp' -o -name '*.h' | xargs clang-format -i"
        find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | xargs clang-format --dry-run

  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: format-check
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y clang make wget
        
    - name: Download Catch2
      run: |
        mkdir -p /tmp/include/Catch2
        wget -P /tmp/include/Catch2 https://github.com/catchorg/Catch2/releases/download/v2.13.10/catch.hpp
        
    - name: Build hello project
      run: |
        cd src/hello
        clang++ --std=c++17 -Wall -Wextra -Wpedantic hello.cpp -o hello.out
        
    - name: Build complex project
      run: |
        cd src/complex
        # Update Makefile to use temporary Catch2 location
        sed -i 's|/workspaces/include/Catch2|/tmp/include/Catch2|g' Makefile
        make clean
        make test
        
    - name: Run complex tests
      run: |
        cd src/complex
        ./test.out
        
    - name: Build container project
      run: |
        cd src/container
        # Update Makefile to use temporary Catch2 location
        sed -i 's|/workspaces/include/Catch2|/tmp/include/Catch2|g' Makefile
        make clean
        make test
        
    - name: Run container tests
      run: |
        cd src/container
        ./test.out
        
    - name: Compilation warnings check
      run: |
        echo "Checking for compilation warnings..."
        cd src/complex
        clang++ --std=c++17 -Wall -Wextra -Wpedantic -Werror -I/tmp/include/Catch2 -c complex.cpp
        clang++ --std=c++17 -Wall -Wextra -Wpedantic -Werror -I/tmp/include/Catch2 -c test.cpp
        cd ../container
        clang++ --std=c++17 -Wall -Wextra -Wpedantic -Werror -I/tmp/include/Catch2 -c vector_ex.cpp
        clang++ --std=c++17 -Wall -Wextra -Wpedantic -Werror -I/tmp/include/Catch2 -c map_ex.cpp
        clang++ --std=c++17 -Wall -Wextra -Wpedantic -Werror -I/tmp/include/Catch2 -c test.cpp
