# C++ Learning Exercises

This repository contains C++ exercises for learning fundamental concepts including basic I/O, object-oriented programming, and STL containers.


## Development Environment Setup

### Option 1: Using Docker (Recommended)

1. **Prerequisites**: Install Docker on your system

2. **Build the development container**:
   ```bash
   docker build -t cpp-exercises .
   ```

3. **Run the container**:
   ```bash
   docker run -it -v $(pwd):/workspaces/cpp-exercises cpp-exercises bash
   ```

4. **Navigate to the project directory**:
   ```bash
   cd /workspaces/cpp-exercises
   ```

### Option 2: Local Setup

1. **Install required tools**:
   ```bash
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install -y clang clang-format make wget

   # macOS (using Homebrew)
   brew install llvm make wget

   # Windows (using MSYS2 or WSL recommended)
   ```

2. **Download Catch2 testing framework**:
   ```bash
   mkdir -p /workspaces/include/Catch2
   wget -P /workspaces/include/Catch2 https://github.com/catchorg/Catch2/releases/download/v2.13.10/catch.hpp
   ```

### VSCode Setup

1. **Install VSCode Extensions**:
   - C/C++ (Microsoft)
   - C/C++ Extension Pack
   - clang-format
   - GitLens (optional)

2. **Configure VSCode settings** (create `.vscode/settings.json`):
   ```json
   {
     "C_Cpp.default.cppStandard": "c++17",
     "C_Cpp.default.compilerPath": "/usr/bin/clang++",
     "C_Cpp.default.includePath": [
       "${workspaceFolder}/src",
       "/workspaces/include"
     ],
     "editor.formatOnSave": true,
     "C_Cpp.clang_format_style": "file"
   }
   ```

3. **Configure build tasks** (create `.vscode/tasks.json`):
   ```json
   {
     "version": "2.0.0",
     "tasks": [
       {
         "label": "Build Complex",
         "type": "shell",
         "command": "make",
         "args": ["test"],
         "group": "build",
         "options": {
           "cwd": "${workspaceFolder}/src/complex"
         }
       },
       {
         "label": "Build Container",
         "type": "shell",
         "command": "make",
         "args": ["test"],
         "group": "build",
         "options": {
           "cwd": "${workspaceFolder}/src/container"
         }
       }
     ]
   }
   ```

## Building and Running Projects

### Hello World Project

```bash
cd src/hello
clang++ --std=c++17 hello.cpp -o hello.out
./hello.out
```

### Complex Numbers Project

```bash
cd src/complex

# Build the project
make test

# Run tests
make run
# or directly: ./test.out

# Clean build files
make clean
```

### Container Exercises Project

```bash
cd src/container

# Build the project
make test

# Run tests
make run
# or directly: ./test.out

# Clean build files
make clean
```

## Available Make Targets

Each project directory (`complex/` and `container/`) includes a Makefile with these targets:

- `make test` - Compile the test executable
- `make run` - Build and run tests
- `make clean` - Remove compiled files

## Running Code Quality Checks

Before committing code, run the local quality checker:

```bash
./scripts/check-code.sh
```

This script will:
- Check code formatting with clang-format
- Compile all projects
- Run all unit tests
- Check for compilation warnings

## Code Formatting

This project uses `clang-format` for consistent code style:

```bash
# Format all C++ files
find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | xargs clang-format -i

# Check formatting without modifying files
find src -name "*.cpp" -o -name "*.hpp" -o -name "*.h" | xargs clang-format --dry-run
```

## Testing

All projects use the Catch2 testing framework. Tests are located in `test.cpp` files within each project directory.

### Running Individual Tests

```bash
# Run specific test cases
cd src/complex
./test.out "Complex numbers are constructed"

# List all available test cases
./test.out --list-tests
```

## Continuous Integration

This repository includes GitHub Actions CI that automatically:
- Checks code formatting
- Compiles all projects
- Runs all tests
- Detects compilation warnings

The CI runs on every push and pull request to main branches.

## Troubleshooting

### Common Build Issues

1. **Catch2 not found**:
   ```bash
   # Ensure Catch2 is downloaded
   wget -P /workspaces/include/Catch2 https://github.com/catchorg/Catch2/releases/download/v2.13.10/catch.hpp
   ```

2. **Compiler not found**:
   ```bash
   # Install clang++
   sudo apt-get install clang
   ```

3. **Permission denied on scripts**:
   ```bash
   chmod +x scripts/check-code.sh
   ```

### VSCode Issues

1. **IntelliSense not working**: Check that include paths are correctly set in `.vscode/settings.json`

2. **Build tasks not found**: Ensure `.vscode/tasks.json` is properly configured

3. **Formatting not working**: Install the clang-format extension and ensure `.clang-format` exists

## Learning Path

1. **Start with Hello World** (`src/hello/`) - Basic C++ syntax and I/O
2. **Complex Numbers** (`src/complex/`) - Classes, operators, and OOP concepts
3. **Containers** (`src/container/`) - STL containers and algorithms

Each project builds upon previous concepts and includes comprehensive unit tests to verify your implementation.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run `./scripts/check-code.sh` to ensure quality
5. Commit and push your changes
6. Create a pull request

The CI system will automatically verify your changes before they can be merged.
