#include "complex.hpp"
#include <cmath>

Complex::Complex(double real) : re(real) {
}

Complex::Complex(double real, double imag) : re(real), im(imag) {
}

double Complex::real() const {
    return re;
}

double Complex::imag() const {
    return im;
}

bool operator==(Complex const& a, Complex const& b) {
    return (a.re == b.re) && (a.im == b.im);
}

bool operator!=(Complex const& a, Complex const& b) {
    return !(a == b);
}

double Complex::norm2() const {
    return im * im + re * re;
}

Complex Complex::conj() const {
    return Complex{re, -im};
}

Complex operator-(Complex const& a) {
    return Complex{-a.re, -a.im};
}

Complex operator+(Complex const& a, Complex const& b) {
    return Complex{a.re + b.re, a.im + b.im};
}

Complex operator-(Complex const& a, Complex const& b) {
    return Complex{a.re - b.re, a.im - b.im};
}

Complex operator*(Complex const& a, Complex const& b) {
    // (a + b*i)(c + d*i) = (ac - bd)(ad*i + bc*i)
    return Complex{
        a.re * b.re - a.im * b.im,
        a.re * b.im + a.im * b.re
    };
}