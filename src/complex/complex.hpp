#ifndef CPPEX_COMPLEX_COMPLEX_HPP
#define CPPEX_COMPLEX_COMPLEX_HPP

// Simple complex number class
class Complex {
public:
  // Default value is zero
  Complex() = default;
  // Construct purely real complex
  Complex(double real);
  // Construct from real and imaginary parts
  Complex(double real, double imag);

  double real() const;
  double imag() const;

  friend bool operator==(Complex const& a, Complex const& b);
  friend bool operator!=(Complex const& a, Complex const& b);

  double norm2() const;

  Complex conj() const;

  friend Complex operator+(Complex const& a, Complex const& b);
  friend Complex operator-(Complex const& a, Complex const& b);
  friend Complex operator*(Complex const& a, Complex const& b);

  friend Complex operator-(Complex const& a);

private:
  double re = 0.0;
  double im = 0.0;
};

#endif