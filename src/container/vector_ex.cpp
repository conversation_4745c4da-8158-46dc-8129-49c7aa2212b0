#include "vector_ex.hpp"

// std::vector documentation:
// https://en.cppreference.com/w/cpp/container/vector

std::vector<int> GetEven(std::vector<int> const& source) {
  std::vector<int> ans;
  for (auto x : source) {
    if (x % 2 == 0) {
      ans.push_back(x);
    }
  }
  return ans;
}

void PrintVectorOfInt(std::ostream& output, std::vector<int> const& data) {
  output << "[ ";
  if (!data.empty()) {
    for (size_t i = 0; i < data.size() - 1; ++i) {
      output << data[i] << ", ";
    }
    output << data[data.size() - 1] << "]";
  } else {
    output << "]";
  }
}