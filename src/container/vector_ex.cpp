#include "vector_ex.hpp"

// std::vector documentation:
// https://en.cppreference.com/w/cpp/container/vector

std::vector<int> GetEven(std::vector<int> const& source) {
    std::vector<int> ans;
    for (int x: source) {
        if (x % 2 == 0) {
            ans.push_back(x);
        }
    }
    return ans;
}

void PrintVectorOfInt(std::ostream& output, std::vector<int> const& data) {
    output << "[ ";
    if (data.size() == 0) {
        output << "]";
        return;
    }
    int i = 0;
    while(i < data.size() - 1) {
        output << data[i] << ", ";
        i += 1; 
    }
    output << data[i] << "]";
}